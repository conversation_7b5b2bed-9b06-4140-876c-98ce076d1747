<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作文报告 - 慧习作</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #ffffff;
            color: #323842;
            width: 375px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        /* Status Bar */
        .status-bar {
            width: 100%;
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #BCC1CA;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-icon {
            width: auto;
            height: auto;
        }

        /* Header */
        .header {
            position: relative;
            height: 72px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 24px;
        }

        .back-button {
            position: absolute;
            left: 24px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-title {
            font-size: 16px;
            font-weight: 400;
            color: #323842;
            line-height: 1.625;
        }

        /* Tab Navigation */
        .tab-nav {
            display: flex;
            margin: 0 23px;
            height: 40px;
            background: transparent;
        }

        .tab-item {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
            background: transparent;
        }

        .tab-text {
            font-size: 11px;
            font-weight: 400;
            color: #565E6C;
            line-height: 1.636;
        }

        .tab-item.active .tab-text {
            font-weight: 700;
            color: #636AE8;
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #636AE8;
        }

        /* Content Area */
        .content-area {
            margin: 8px 18px;
            height: 701px;
            border: 3px solid #BCC1CA;
            position: relative;
            background: white;
            overflow: hidden;
        }

        .content-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .content-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            overflow-y: auto;
        }

        /* Report Content */
        .report-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .report-title {
            font-size: 18px;
            font-weight: 700;
            color: #323842;
            margin-bottom: 10px;
        }

        .report-subtitle {
            font-size: 14px;
            color: #565E6C;
        }

        .chart-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            height: 120px;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            gap: 8px;
            height: 80px;
        }

        .chart-bar {
            width: 20px;
            background: #636AE8;
            border-radius: 2px;
        }

        .bar-1 { height: 20%; }
        .bar-2 { height: 60%; }
        .bar-3 { height: 80%; }
        .bar-4 { height: 40%; }
        .bar-5 { height: 100%; }
        .bar-6 { height: 30%; }
        .bar-7 { height: 15%; }

        .report-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #323842;
            margin-bottom: 10px;
        }

        .section-content {
            font-size: 14px;
            color: #565E6C;
            line-height: 1.5;
        }

        .score-highlight {
            background: #636AE8;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-left">
            <img src="assets/signal_icon.svg" alt="Signal" class="status-icon">
            <img src="assets/wifi_icon.svg" alt="WiFi" class="status-icon">
        </div>
        <div class="status-right">
            <img src="assets/chart_outline.svg" alt="Chart" class="status-icon">
            <img src="assets/battery_icon.svg" alt="Battery" class="status-icon">
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="back-button" onclick="goBack()">
            <img src="assets/arrow_line.svg" alt="Back" style="position: absolute;">
            <img src="assets/arrow_head.svg" alt="Back" style="position: absolute;">
        </div>
        <div class="header-title">详情</div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-nav">
        <div class="tab-item" onclick="switchTab('review')">
            <span class="tab-text">作文点评</span>
        </div>
        <div class="tab-item active" onclick="switchTab('report')">
            <span class="tab-text">作文报告</span>
        </div>
        <div class="tab-item" onclick="switchTab('polish')">
            <span class="tab-text">润色范文</span>
        </div>
        <div class="tab-item" onclick="switchTab('requirements')">
            <span class="tab-text">作文要求</span>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <img src="assets/content_bg1.svg" alt="Background" class="content-bg">
        <div class="content-overlay">
            <div class="report-header">
                <div class="report-title">作文分析报告</div>
                <div class="report-subtitle">基于AI智能分析生成</div>
            </div>

            <div class="chart-container">
                <div class="chart-bars">
                    <div class="chart-bar bar-1"></div>
                    <div class="chart-bar bar-2"></div>
                    <div class="chart-bar bar-3"></div>
                    <div class="chart-bar bar-4"></div>
                    <div class="chart-bar bar-5"></div>
                    <div class="chart-bar bar-6"></div>
                    <div class="chart-bar bar-7"></div>
                </div>
            </div>

            <div class="report-section">
                <div class="section-title">总体评价</div>
                <div class="section-content">
                    本次作文整体表现良好，获得 <span class="score-highlight">28分</span>。文章主题明确，内容丰富，结构清晰，语言表达流畅。在思想深度和创新性方面还有提升空间。
                </div>
            </div>

            <div class="report-section">
                <div class="section-title">优点分析</div>
                <div class="section-content">
                    • 主题鲜明，紧扣题意<br>
                    • 内容具体，描述生动<br>
                    • 结构完整，层次分明<br>
                    • 语言准确，表达清晰
                </div>
            </div>

            <div class="report-section">
                <div class="section-title">改进建议</div>
                <div class="section-content">
                    • 可以增加更多个人感悟和思考<br>
                    • 适当运用修辞手法增强表现力<br>
                    • 注意段落间的过渡和衔接<br>
                    • 结尾可以更加深刻和有力
                </div>
            </div>

            <div class="report-section">
                <div class="section-title">写作水平</div>
                <div class="section-content">
                    根据分析，您的写作水平处于中等偏上水平。继续保持良好的写作习惯，多读优秀范文，勤加练习，相信会有更大的进步。
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            // 返回上一页或跳转到指定页面
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，跳转到首页
                window.location.href = 'index.html';
            }
        }

        function switchTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加active类到当前点击的tab
            event.currentTarget.classList.add('active');
            
            // 根据tab名称跳转到不同页面
            switch(tabName) {
                case 'review':
                    window.location.href = 'composition-review.html';
                    break;
                case 'report':
                    // 当前页面，不需要跳转
                    break;
                case 'polish':
                    window.location.href = 'composition-polish.html';
                    break;
                case 'requirements':
                    window.location.href = 'composition-requirements.html';
                    break;
            }
        }
    </script>
</body>
</html>
